import React from 'react';
import EmptyState from '../../../components/EmptyState';
import { modalShow } from '@/components/List';
import styles from './index.module.scss';
import pause from '@/Resources/nasDiskImg/pause.png'
import play from '@/Resources/nasDiskImg/play.png'
import del from '@/Resources/nasDiskImg/del.png'
import restart from '@/Resources/nasDiskImg/restart.png'
import file_icon from '@/Resources/nasDiskImg/file-icon.png'
import { PreloadImage } from '@/components/Image';
import { Progress } from 'antd';

export interface TabItem {
  key: string;
  label: string;
  count: number;
  status?: 'active' | 'error' | 'completed';
}

export interface TaskItem {
  id: string;
  name: string;
  size: string;
  progress: number;
  status: string;
  speed?: string;
  storageLocation?: string; // 存储位置
  completedTime?: string; // 完成时间
}

interface TaskListProps {
  tabs: TabItem[];
  activeTab: string;
  tasks: TaskItem[];
  loading?: boolean;
  emptyTitle?: string;
  emptyDescription?: string;
  onTabChange: (tabKey: string) => void;
  onTaskAction?: (taskId: string, action: string) => void;
  onDeleteAll?: () => void;
  onPauseAll?: () => void;
}

const TaskList: React.FC<TaskListProps> = ({
  tabs,
  activeTab,
  tasks,
  loading = false,
  emptyTitle = "暂无内容",
  emptyDescription,
  onTabChange,
  onTaskAction,
  onDeleteAll,
  onPauseAll
}) => {
  const getTabClassName = (tab: TabItem) => {
    let className = styles.tab_item;

    // 只有真正选中的tab才添加active类
    if (tab.key === activeTab) {
      className += ` ${styles.active}`;
    } else if (tab.status) {
      // 只有在非选中状态下才添加状态类
      className += ` ${styles[tab.status]}`;
    }

    return className;
  };

  const handleTaskAction = (taskId: string, action: string) => {
    if (onTaskAction) {
      onTaskAction(taskId, action);
    }
  };

  const handleDeleteAllClick = () => {
    modalShow(
      '删除任务',
      <div className={styles.delete_modal_content}>
        确定删除全部任务文件？已上传完成的文件不会被删除
      </div>,
      (m) => {
        if (onDeleteAll) {
          onDeleteAll();
        }
        m.destroy();
      },
      undefined,
      false,
      {
        okBtnText: '确定',
        cancelBtnText: '取消',
        position: 'center',
        okBtnStyle: {
          backgroundColor: '#32BAC0',
          borderColor: '#32BAC0',
          color: '#fff'
        }
      }
    );
  };

  const handlePauseAllClick = () => {
    if (onPauseAll) {
      onPauseAll();
    }
  };

  const renderProgressBar = (progress: number, status: string) => {
    return (
      <Progress
        percent={progress}
        showInfo={false}
        className={`${styles.progressBar} ${styles[status]}`}
      />
    );
  };

  const renderTaskItem = (task: TaskItem) => {
    const isCompleted = activeTab === 'completed';

    // 根据任务状态和tab类型渲染操作按钮
    const renderActionButtons = () => {
      if (isCompleted) {
        return null; // 已完成任务不显示操作按钮
      }

      if (activeTab === 'failed') {
        // 失败存储Tab：显示task_light + delete
        return (
          <div className={styles.task_actions}>
            <button
              className={styles.action_btn_two}
              onClick={() => handleTaskAction(task.id, 'retry')}
            >
              <PreloadImage style={{ width: '17px', height: '17px' }} src={restart} alt="重试" />
            </button>
            <button
              className={styles.action_btn_two}
              onClick={() => handleTaskAction(task.id, 'delete')}
            >
              <PreloadImage style={{ width: '17px', height: '17px' }} src={del} alt="删除" />
            </button>
          </div>
        );
      }

      if (activeTab === 'downloading' || activeTab === 'uploading') {
        // 正在下载/上传Tab：根据状态显示pause/play + delete
        const isWaitingOrRunning = task.status === '等待中' || task.status === '进行中';
        const isPaused = task.status === '已暂停';

        return (
          <div className={styles.task_actions}>
            {isWaitingOrRunning && (
              <button
                className={styles.action_btn_two}
                onClick={() => handleTaskAction(task.id, 'pause')}
              >
                <PreloadImage style={{ width: '17px', height: '17px' }} src={pause} alt="暂停" />
              </button>
            )}
            {isPaused && (
              <button
                className={styles.action_btn_two}
                onClick={() => handleTaskAction(task.id, 'resume')}
              >
                <PreloadImage style={{ width: '17px', height: '17px' }} src={play} alt="开始" />
              </button>
            )}
            <button
              className={styles.action_btn_two}
              onClick={() => handleTaskAction(task.id, 'delete')}
            >
              <PreloadImage style={{ width: '17px', height: '17px' }} src={del} alt="删除" />
            </button>
          </div>
        );
      }

      return null;
    };

    return (
      <div key={task.id} className={styles.task_item}>
        <div className={styles.task_info}>
          <div className={styles.task_icon}>
            <PreloadImage style={{ width: '40px', height: '40px' }} src={file_icon} alt="文件" />
          </div>
          <div className={styles.task_content}>
            <div className={styles.task_name} title={task.name}>
              {task.name}
            </div>
            {!isCompleted ? (
              <>
                {renderProgressBar(task.progress, task.status)}
              </>
            ) : null}
          </div>
        </div>

        {isCompleted ? (
          <>
            {/* 已完成状态：存储位置 */}
            <div className={styles.task_storage}>
              <span className={styles.storage_location}>
                {task.storageLocation || ''}
              </span>
            </div>

            {/* 已完成状态：大小 */}
            <div className={styles.task_size_column}>
              <span className={styles.file_size}>
                {task.size?.split('/')?.pop()?.trim() ?? ''}
              </span>
            </div>

            {/* 已完成状态：完成时间 */}
            <div className={styles.task_completed_time}>
              <span className={styles.completed_time}>
                {task.completedTime || '2016-06-18 21:00'}
              </span>
            </div>
          </>
        ) : (
          <>
            {/* 进行中状态：任务进度 */}
            <div className={styles.task_progress}>
              <div className={styles.task_progress_info}>
                <div className={styles.task_details}>
                  {task.status === '进行中' ? (<>
                    {task.speed && <span>{task.speed}</span>}
                  </>) : (<>
                    <span className={styles.task_status}>{task.status}</span>
                  </>)}

                </div>
              </div>
            </div>
            <div className={styles.task_progress}>
              <div className={styles.task_progress_info}>
                <div className={styles.task_speed}>
                  <span className={styles.task_size}>{task.size}</span>
                </div>
              </div>
            </div>
            {/* 动态操作按钮 */}
            {renderActionButtons()}
          </>
        )}
      </div>
    );
  };

  return (
    <div className={`${styles.task_list_container} ${activeTab === 'completed' ? styles.completed : ''}`}>
      {/* Tab 切换头部 */}
      <div className={styles.tab_header}>
        <div className={styles.tab_list}>
          {tabs.map((tab) => (
            <div
              key={tab.key}
              className={getTabClassName(tab)}
              onClick={() => onTabChange(tab.key)}
            >
              <span className={styles.tab_label}>{tab.label}</span>
              <span className={styles.tab_count}>({tab.count})</span>
            </div>
          ))}
        </div>

        <div className={styles.tab_actions}>
          {/* 在这里显示一个小的loading指示器，而不是替换整个组件 */}
          {loading && (
            <div className={styles.loading_indicator}>
              <div className={styles.loading_dot}></div>
              <span>更新中...</span>
            </div>
          )}
          {activeTab !== 'completed' && (
            <button className={styles.action_btn} onClick={handlePauseAllClick}>
              {/* <PreloadImage style={{ width: '17px', height: '17px' }} src={pause} alt="全部暂停" /> */}
              全部暂停
            </button>
          )}
          <button className={styles.action_btn} onClick={handleDeleteAllClick}>
            全部删除
          </button>
        </div>
      </div>

      {/* 列表内容区域 */}
      <div className={styles.task_content}>
        {tasks.length === 0 ? (
          <EmptyState
            title={emptyTitle}
            description={emptyDescription}
          />
        ) : (
          <div className={styles.task_list}>
            {/* 列表头部 */}
            <div className={styles.list_header}>
              <div className={styles.header_item}>全部文件 | {tasks.length}项</div>
              {activeTab === 'completed' ? (
                <>
                  <div className={styles.header_item}>位置</div>
                  <div className={styles.header_item}>大小</div>
                  <div className={styles.header_item}>时间</div>
                </>
              ) : (
                <>
                  <div className={styles.header_item}>状态</div>
                  <div className={styles.header_item}>大小</div>
                  {/* <div className={styles.header_item}>操作</div> */}
                </>
              )}
            </div>

            {/* 任务列表 */}
            <div className={styles.list_body}>
              {tasks.map(renderTaskItem)}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TaskList; 