import React, { useState, useEffect, useCallback } from 'react';
import TaskList, { TabItem, TaskItem } from '../components/TaskList';
import { TaskInfo, deleteHistoryTask } from '@/api/nasDisk';
import request from '@/request';
import CommonUtils from '@/utils/CommonUtils';

const DownloadNas: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>('downloading');
  const [tasks, setTasks] = useState<TaskItem[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [taskCounts, setTaskCounts] = useState<Record<string, number>>({
    downloading: 0,
    failed: 0,
    completed: 0
  });

  // 将 TaskInfo 转换为 TaskItem
  const convertTaskInfoToTaskItem = useCallback((taskInfo: TaskInfo): TaskItem => {
    const { task_id, src, dst, status, detail, done_time } = taskInfo;
    
    // 获取文件名 - 只从src[0]路径中取最后一个"/"后面的名称
    const getFileName = (): string => {
      if (src && src.length > 0 && src[0]) {
        const path = src[0];
        // 找到最后一个"/"的位置
        const lastSlashIndex = path.lastIndexOf('/');
        if (lastSlashIndex !== -1 && lastSlashIndex < path.length - 1) {
          // 返回最后一个"/"后面的部分
          return path.substring(lastSlashIndex + 1);
        }
        // 如果没有"/"或者"/"在最后，返回整个路径
        return path;
      }
      
      return '未知文件';
    };
    
    const fileName = getFileName();
    
    // 状态映射
    const statusMap: Record<string, string> = {
      'waiting': '等待中',
      'running': '进行中',
      'paused': '已暂停',
      'success': '已完成',
      'failed': '失败存储',
      'cancelled': '已取消',
      'partial error': '部分失败'
    };
    
    // 格式化文件大小
    const formatFileSize = (bytes: string | number): string => {
      const size = typeof bytes === 'string' ? parseInt(bytes) : bytes;
      if (size === 0) return '0 B';
      
      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
      const i = Math.floor(Math.log(size) / Math.log(k));
      
      return parseFloat((size / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };
    
    // 计算进度 - 使用 handled_size/total_size
    const calculateProgress = (): number => {
      if (detail?.handled_size && detail?.total_size) {
        const handledSize = typeof detail.handled_size === 'string' ? parseInt(detail.handled_size) : detail.handled_size;
        const totalSize = typeof detail.total_size === 'string' ? parseInt(detail.total_size) : detail.total_size;
        
        if (totalSize > 0) {
          return Math.round((handledSize / totalSize) * 100);
        }
      }
      return 0;
    };
    
    // 计算大小显示
    const getSizeDisplay = () => {
      if (detail?.handled_size && detail?.total_size) {
        const handledSize = formatFileSize(detail.handled_size);
        const totalSize = formatFileSize(detail.total_size);
        return `${handledSize} / ${totalSize}`;
      }
      return detail?.total_size ? formatFileSize(detail.total_size) : '未知大小';
    };
    
    // 格式化完成时间
    const formatCompletedTime = (timestamp: string) => {
      if (!timestamp) return undefined;
      // 处理时间戳（可能是毫秒或秒）
      const time = timestamp.length > 10 ? parseInt(timestamp) : parseInt(timestamp) * 1000;
      const date = new Date(time);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    };
    
    // 处理速度显示 - 如果为空则显示100kb/s
    const getSpeedDisplay = (): string => {
      if (detail?.speed && detail.speed.trim() !== '') {
        return detail.speed;
      }
      
      // 如果是进行中状态且速度为空，返回假数据
      if (status === 'running') {
        return '100KB/s';
      }
      
      // 其他状态返回空字符串
      return '';
    };
    
    return {
      id: task_id,
      name: fileName,
      size: getSizeDisplay(),
      progress: calculateProgress(),
      status: statusMap[status] || status,
      speed: getSpeedDisplay(),
      storageLocation: CommonUtils.formatFilePath(dst),
      completedTime: status === 'success' ? formatCompletedTime(done_time) : undefined
    };
  }, []);

  // 初始化获取tab数量
  const fetchTabCounts = useCallback(async () => {
    try {
      // 获取历史任务，统计失败和成功数量
      const historyResponse = await request.post('/taskcenter/get_taskinfo', {
        selector: [
          {
            key: "module",
            value: ["bpan"]
          },
          {
            key: "type",
            value: ["history"]
          },
          {
            key: "action",
            value: ["download"]
          }
        ]
      }, {
        showLoading: false
      });

      // 获取进行中任务数量
      const activeResponse = await request.post('/taskcenter/get_taskinfo', {
        selector: [
          {
            key: "module",
            value: ["bpan"]
          },
          {
            key: "type",
            value: ["active"]
          },
          {
            key: "action",
            value: ["download"]
          }
        ]
      }, {
        showLoading: false
      });

      let downloadingCount = 0;
      let failedCount = 0;
      let completedCount = 0;

      // 统计进行中任务数量
      if (activeResponse.code === 0 && activeResponse.data?.page) {
        downloadingCount = activeResponse.data.page.total_task || 0;
      }

      // 统计失败和成功任务数量
      if (historyResponse.code === 0 && historyResponse.data?.info) {
        const historyTasks = historyResponse.data.info;
        
        historyTasks.forEach((task: any) => {
          switch (task.status) {
            case 'failed':
            case 'partial error':
              failedCount++;
              break;
            case 'success':
              completedCount++;
              break;
          }
        });
      }

      // 更新任务计数
      setTaskCounts({
        downloading: downloadingCount,
        failed: failedCount,
        completed: completedCount
      });

    } catch (error) {
      console.error('获取tab数量失败:', error);
      // 使用默认值
      setTaskCounts({
        downloading: 0,
        failed: 0,
        completed: 0
      });
    }
  }, []);

  // 调用API获取任务信息
  const fetchTaskInfo = useCallback(async (showLoading = false) => {
    try {
      // 根据当前tab决定请求参数
      let selector;
      
      if (activeTab === 'downloading') {
        // 正在下载：使用active类型
        selector = [
          {
            key: "module",
            value: ["bpan"]
          },
          {
            key: "type",
            value: ["active"]
          },
          {
            key: "action",
            value: ["download"]
          }
        ];
      } else if (activeTab === 'failed') {
        // 失败存储：使用history类型 + failed状态
        selector = [
          {
            key: "module",
            value: ["bpan"]
          },
          {
            key: "type",
            value: ["history"]
          },
          {
            key: "action",
            value: ["download"]
          },
          {
            key: "status",
            value: ["failed"]
          },

        ];
      } else if (activeTab === 'completed') {
        // 已完成：使用history类型 + success状态
        selector = [
          {
            key: "module",
            value: ["bpan"]
          },
          {
            key: "type",
            value: ["history"]
          },
          {
            key: "action",
            value: ["download"]
          },
          {
            key: "status",
            value: ["success"]
          }
        ];
      }
      
      const response = await request.post('/taskcenter/get_taskinfo', {
        selector: selector
      }, {
        showLoading: showLoading  // 控制全局loading的显示
      });

      if (response.code === 0 && response.data?.info) {
        const taskInfos: TaskInfo[] = response.data.info;
        
        // 按状态分组任务
        const groupedTasks: Record<string, TaskItem[]> = {
          downloading: [],
          failed: [],
          completed: []
        };
        
        taskInfos.forEach(taskInfo => {
          const taskItem = convertTaskInfoToTaskItem(taskInfo);
          
          switch (taskInfo.status) {
            case 'waiting':
            case 'running':
            case 'paused':
              groupedTasks.downloading.push(taskItem);
              break;
            case 'failed':
            case 'cancelled':
            case 'partial error':
              groupedTasks.failed.push(taskItem);
              break;
            case 'success':
              groupedTasks.completed.push(taskItem);
              break;
            default:
              groupedTasks.downloading.push(taskItem);
          }
        });
        
        // 只有进行中Tab才更新数量，失败和已完成Tab保持初始化时的数量
        if (activeTab === 'downloading') {
          // 进行中Tab：只更新进行中任务数量，保持失败和已完成数量不变
          setTaskCounts(prevCounts => ({
            ...prevCounts,
            downloading: groupedTasks.downloading.length
          }));
        }
        
        // 设置当前tab的任务
        setTasks(groupedTasks[activeTab] || []);
      } else {
        // 如果没有数据，设置为空数组
        setTasks([]);
      }
    } catch (error) {
      console.error('获取任务信息失败:', error);
      // 接口调用失败，设置为空数组
      setTasks([]);
    } finally {
      if (showLoading) {
        setLoading(false);
      }
    }
  }, [activeTab, convertTaskInfoToTaskItem]);

  const tabs: TabItem[] = [
    {
      key: 'downloading',
      label: '进行中',
      count: taskCounts.downloading
    },
    {
      key: 'failed',
      label: '失败任务',
      count: taskCounts.failed,
      status: 'error'
    },
    {
      key: 'completed',
      label: '已完成',
      count: taskCounts.completed
    }
  ];

  // 初始化获取tab数量
  useEffect(() => {
    fetchTabCounts();
  }, [fetchTabCounts]);

  // 获取当前tab的任务数据
  useEffect(() => {
    // 首次加载显示loading
    fetchTaskInfo(true);
    
    // 只有在正在下载Tab时才设置轮询
    if (activeTab === 'downloading') {
      const interval = setInterval(() => {
        fetchTaskInfo(false); // 静默轮询，不显示loading，只轮询active接口
      }, 5000);
      
      // 清理定时器
      return () => clearInterval(interval);
    }
  }, [activeTab, fetchTaskInfo]); // 依赖activeTab和fetchTaskInfo

  const handleTabChange = (tabKey: string) => {
    setActiveTab(tabKey);
  };

  const handleTaskAction = useCallback(async (taskId: string, action: string) => {
    console.log(`执行任务操作: ${action} - ${taskId}`);
    
    // 将前端action映射到后端command
    const actionToCommandMap: Record<string, string> = {
      'pause': 'pause',      // 暂停
      'resume': 'continue',  // 恢复 -> 继续
      'delete': 'cancel',    // 删除 -> 取消
      'retry': 'restart'     // 重试 -> 重新开始
    };
    
    const command = actionToCommandMap[action];
    if (!command) {
      console.error('未知的任务操作:', action);
      return;
    }
    
    try {
      const response = await request.post('/taskcenter/ctrl_task', {
        task_id: [taskId],  // 任务ID数组
        command: command    // 控制命令
      }, {
        showLoading: true  // 显示loading，因为这是用户主动操作
      });
      
      if (response.code === 0) {
        console.log(`任务${action}操作成功:`, taskId);
        // 操作成功后立即刷新数据
        await fetchTaskInfo(false);
      } else {
        console.error(`任务${action}操作失败:`, response.message || '未知错误');
        // 可以在这里显示错误提示
      }
    } catch (error) {
      console.error(`任务${action}操作异常:`, error);
      // 可以在这里显示错误提示
    }
  }, [fetchTaskInfo]);

  const handlePauseAll = useCallback(async () => {
    console.log('全部暂停下载任务');
    
    // 获取当前tab中可以暂停的任务ID（waiting和running状态）
    const pausableTaskIds = tasks
      .filter(task => task.status === '等待中' || task.status === '进行中')
      .map(task => task.id);
    
    if (pausableTaskIds.length === 0) {
      console.log('没有可暂停的任务');
      return;
    }
    
    try {
      const response = await request.post('/taskcenter/ctrl_task', {
        task_id: pausableTaskIds,
        command: 'pause'
      }, {
        showLoading: true
      });
      
      if (response.code === 0) {
        console.log('批量暂停任务成功:', pausableTaskIds);
        // 操作成功后立即刷新数据
        await fetchTaskInfo(false);
      } else {
        console.error('批量暂停任务失败:', response.message || response.result || '未知错误');
        // 可以在这里显示错误提示
      }
    } catch (error) {
      console.error('批量暂停任务异常:', error);
      // 可以在这里显示错误提示
    }
  }, [tasks, fetchTaskInfo]);

  const handleDeleteAll = useCallback(async () => {
    console.log('删除全部下载任务');
    
    // 获取当前tab的所有任务ID
    const taskIds = tasks.map(task => task.id);
    
    if (taskIds.length === 0) {
      console.log('没有任务需要删除');
      return;
    }
    
    try {
      if (activeTab === 'completed') {
        // 已完成tab：调用删除历史任务接口
        const response = await deleteHistoryTask({
          selector: {
            key: 'task_id',
            value: taskIds
          }
        });
        
        if (response.code === 0) {
          console.log('删除历史任务成功:', taskIds);
          // 操作成功后立即刷新数据
          await fetchTaskInfo(false);
          // 重新获取tab数量
          await fetchTabCounts();
        } else {
          console.error('删除历史任务失败:', response.result);
          // 可以在这里显示错误提示
        }
      } else {
        // 进行中和失败任务tab：调用控制任务接口
        const response = await request.post('/taskcenter/ctrl_task', {
          task_id: taskIds,     // 所有任务ID数组
          command: 'cancel'     // 取消命令
        }, {
          showLoading: true     // 显示loading
        });
        
        if (response.code === 0) {
          console.log('批量取消任务成功:', taskIds);
          // 操作成功后立即刷新数据
          await fetchTaskInfo(false);
          // 重新获取tab数量
          await fetchTabCounts();
        } else {
          console.error('批量取消任务失败:', response.message || response.result || '未知错误');
          // 可以在这里显示错误提示
        }
      }
    } catch (error) {
      console.error('批量删除任务异常:', error);
      // 可以在这里显示错误提示
    }
  }, [activeTab, tasks, fetchTaskInfo, fetchTabCounts]);

  return (
    <TaskList
      tabs={tabs}
      activeTab={activeTab}
      tasks={tasks}
      loading={loading}
      emptyTitle=""
      emptyDescription=""
      onTabChange={handleTabChange}
      onTaskAction={handleTaskAction}
      onDeleteAll={handleDeleteAll}
      onPauseAll={handlePauseAll}
    />
  );
};

export default DownloadNas;