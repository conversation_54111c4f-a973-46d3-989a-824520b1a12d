import React, { useState, useMemo, useEffect, useCallback } from "react";
import NavigatorBar from "@/components/NavBar";
import styles from "./index.module.scss";
import filterIcon from "@/Resources/camMgmtImg/videoEdit.png";
import filterDarkIcon from "@/Resources/camMgmtImg/videoEdit.png";
import dateSelect from '@/Resources/player/dateSelect.png';
import { PreloadImage } from "@/components/Image";
import { useTheme } from "@/utils/themeDetector";
import { useHistory } from "react-router-dom";
import PopoverSelector from "@/components/PopoverSelector";
import DatePicker from "@/components/CameraPlayer/components/DatePicker/DatePicker";
import { format } from "date-fns";
import { zhCN } from "date-fns/locale";
import { useRequest } from "ahooks";
import { listRecordCamera, CameraInfo, getVideoRecord } from "@/api/ipc";
import VideoEditModal from "./VideoEditModal";
import { IlLookBackData } from "@/components/CameraPlayer/components/plugin/ControlPlugin/PlayerControl";
import { downloadFiles } from "@/api/fatWallJSBridge";
import { Toast } from "@/components/Toast/manager";
import { splitURL } from "@/components/CameraPlayer/components/MonitorPlayer/MonitorPlayer";
// import test1 from "@/assets/00_20250804113143_20250804113202.mp4"
// import test2 from "@/assets/xgplayer-demo-360p (2).mp4"

// 视频项接口，基于getVideoRecord接口返回的数据结构
interface VideoItem {
  camera_lens: string;
  event_name: string;
  time: string; // 时间戳
  media_duration: number;
  file: string;
  create_time: string;
  face_info: {
    uuid: string;
    name: string;
    profile_pic: string;
  }[];
  // 计算属性
  timeLabel?: string; // 显示的时间，如 "14:00"
  thumbnail?: string; // 缩略图
  cover_file?: string;
}

// 摄像机选项接口
interface CameraOption {
  label: string;
  value: string;
}

// 默认摄像机选项数据（作为fallback）
const defaultCameraOptions: CameraOption[] = [
  { label: "摄像机01", value: "camera01" },
  { label: "摄像机02", value: "camera02" },
  { label: "摄像机03", value: "camera03" },
];

export default function VideoList() {
  const { isDarkMode } = useTheme();
  const history = useHistory();

  // 视频数据状态
  const [videoData, setVideoData] = useState<VideoItem[]>();
  const [pageInfo] = useState({ size: 20, token: "" });

  // 摄像机数据状态
  const [cameraList, setCameraList] = useState<CameraInfo[]>([]);
  const [cameraOptions, setCameraOptions] = useState<CameraOption[]>(defaultCameraOptions);

  // 摄像机选择器状态
  const [selectedCamera, setSelectedCamera] = useState<string>("camera01");
  const [cameraPopoverVisible, setCameraPopoverVisible] = useState<boolean>(false);

  // 日期选择器状态
  const [selectedDate, setSelectedDate] = useState<Date | null>(null); // 初始为null，表示未选择日期
  const [datePickerVisible, setDatePickerVisible] = useState<boolean>(false);

  // 缩略图状态
  const [videoThumbnails, setVideoThumbnails] = useState<Map<string, string>>(new Map()); // 存储每个视频的缩略图

  // 编辑模态框状态
  const [editModalVisible, setEditModalVisible] = useState<boolean>(false);

  // 生成缩略图URL - 使用cover_file字段拼接/original.jpg
  const generateThumbnailUrl = useCallback((coverFile: string) => {
    if (!coverFile) return '';
    return `${coverFile}/original.jpg`;
  }, []);

  // 设置所有视频的缩略图URL - 使用cover_file字段
  const setAllThumbnails = useCallback(() => {
    if (videoData && videoData.length > 0) {
      const newThumbnails = new Map<string, string>();

      videoData.forEach(video => {
        if (video.cover_file) {
          const videoKey = `${video.camera_lens}-${video.time}`;
          const thumbnailUrl = generateThumbnailUrl(video.cover_file);
          newThumbnails.set(videoKey, thumbnailUrl);
        }
      });

      setVideoThumbnails(newThumbnails);
    }
  }, [videoData, generateThumbnailUrl]);



  // 请求摄像机列表
 useRequest(
    () => listRecordCamera({ did: [] }),
    {
      onSuccess: (res) => {
        if (res.code === 0 && res.data?.camera) {
          const cameras = res.data.camera;
          setCameraList(cameras);

          // 转换为选项格式，使用model字段作为显示名称
          const options: CameraOption[] = cameras.map((camera) => ({
            label: camera.name,
            value: camera.did,
          }));

          if (options.length > 0) {
            setCameraOptions(options);
            // 如果当前选择的摄像机不在新列表中，选择第一个
            if (!options.find(opt => opt.value === selectedCamera)) {
              setSelectedCamera(options[0].value);
            }
          }
        }
      },
      onError: (err) => {
        console.error("获取摄像机列表失败:", err);
        // 保持使用默认数据
        setCameraOptions(defaultCameraOptions);
      },
    }
  );

  // 生成camera_lens参数
  const generateCameraLensParams = useMemo(() => {
    const selectedCameraInfo = cameraList.find(camera => camera.did === selectedCamera);
    if (selectedCameraInfo && selectedCameraInfo.key_frame.length > 0) {
      return selectedCameraInfo.key_frame.map(frame => `${selectedCameraInfo.did}_${frame.lens_id}`);
    }
    return [""]; 
  }, [selectedCamera, cameraList]);

  // 请求视频数据
  const { run: fetchVideoData } = useRequest(
    (params: any) => getVideoRecord(params),
    {
      manual: true,
      onSuccess: (res) => {
        if (res.code === 0 && res.data?.videos) {
          const videos = res.data.videos.map((video) => ({
            ...video,
            timeLabel: generateTimeRangeLabel(video.time, video.media_duration),
          }));
          setVideoData(videos);
          // setPageInfo(res.data.page);
        }
      },
      onError: (err) => {
        console.error("获取视频数据失败:", err);
      },
    }
  );

  // 当视频数据更新后设置缩略图
  useEffect(() => {
    if (videoData && videoData.length > 0) {
      // 直接设置缩略图URL，无需延迟
      setAllThumbnails();
    }
  }, [videoData, setAllThumbnails]);

  // 获取当前选择的摄像机标签
  const selectedCameraLabel = useMemo(() => {
    const camera = cameraOptions?.find(option => option.value === selectedCamera);
    return camera?.label || "摄像机01";
  }, [selectedCamera, cameraOptions]);

  // 格式化日期显示
  const dateDisplayText = useMemo(() => {
    if (!selectedDate) {
      return "选择日期"; // 未选择日期时的默认显示
    }
    const today = new Date();
    const isToday = selectedDate.toDateString() === today.toDateString();
    return isToday ? "今天" : format(selectedDate, 'M月dd日', { locale: zhCN });
  }, [selectedDate]);

  // 统一的视频数据获取逻辑
  useEffect(() => {
    if (generateCameraLensParams.length > 0) {
      let params;

      if (selectedDate) {
        // 如果选择了日期，获取指定日期的视频数据
        const startOfDay = new Date(selectedDate);
        startOfDay.setHours(0, 0, 0, 0);
        const endOfDay = new Date(selectedDate);
        endOfDay.setHours(23, 59, 59, 999);

        params = {
          page: pageInfo,
          options: {
            option: ["camera_lens", "time"],
            camera_lens: generateCameraLensParams,
            time: {
              start: Math.floor(startOfDay.getTime() / 1000).toString(),
              end: Math.floor(endOfDay.getTime() / 1000).toString(),
            },
          },
        };
      } else {
        // 如果没有选择日期，获取所有视频数据
        params = {
          page: pageInfo,
          options: {
            option: ["camera_lens"],
            camera_lens: generateCameraLensParams,
          },
        };
      }

      fetchVideoData(params);
    }
  }, [selectedCamera, selectedDate, generateCameraLensParams, pageInfo, fetchVideoData]);

  const handleBack = () => {
    history.goBack()
  };

  const handleDateSelect = (date: Date) => {
    setSelectedDate(date);
    setDatePickerVisible(false);
  };

  // 打开编辑模态框
  const handleOpenEditModal = () => {
    setEditModalVisible(true);
  };

  // 关闭编辑模态框
  const handleCloseEditModal = () => {
    setEditModalVisible(false);
  };

  // 保存到本机
  const handleSaveToLocal = (selectedVideos: VideoItem[]) => {
    if (selectedVideos.length === 0) {
      Toast.show('请选择要下载的视频');
      return;
    }

    // 构建文件信息数组
    const fileList = selectedVideos.map(video => ({
      name: video.file.split('/').pop() || '', // 从路径中提取文件名
      path: video.file,
    }));

    downloadFiles(fileList, (res) => {
      if (res && res.code === 0) {
        Toast.show('正在下载');
      } else {
        Toast.show('下载失败，请稍后再试');
      }
    });
  };

  // 删除视频
  const handleDeleteVideos = (selectedVideos: VideoItem[]) => {
    console.log('删除视频:', selectedVideos);

    // 删除成功后重新获取视频数据
    const refreshVideoData = () => {
      if (selectedDate) {
        // 如果有选择日期，使用时间参数获取数据
        const dateStr = format(selectedDate, 'yyyy-MM-dd');
        const params = {
          page: pageInfo,
          options: {
            option: ["camera_lens", "time"],
            camera_lens: generateCameraLensParams,
            time: dateStr,
          },
        };
        fetchVideoData(params);
      } else {
        // 如果没有选择日期，使用默认参数获取数据
        const params = {
          page: pageInfo,
          options: {
            option: ["camera_lens"],
            camera_lens: generateCameraLensParams,
          },
        };
        fetchVideoData(params);
      }
    };

    // 延迟一下再刷新，确保删除操作完成
    setTimeout(refreshVideoData, 200);
  };

  // 生成时间范围标签（开始时间-结束时间）
  const generateTimeRangeLabel = (startTimestamp: string, durationSeconds: number): string => {
    try {
      const startTime = parseInt(startTimestamp) * 1000;
      const endTime = startTime + (durationSeconds * 1000);

      const startDate = new Date(startTime);
      const endDate = new Date(endTime);

      const startTimeStr = format(startDate, 'HH:mm');
      const endTimeStr = format(endDate, 'HH:mm');

      return `${startTimeStr}-${endTimeStr}`;
    } catch (error) {
      console.error('时间范围标签生成失败:', error);
      return '00:00-00:00';
    }
  };

  // 视频回放
  const lookBackDetail = useCallback(
    (video: VideoItem) => {
      const lookBackData: IlLookBackData = {
        type: "movie",
        url: video?.file,
        // url: test1,

      };
      history.push({
        pathname: "/cameraManagement_app/cameraDetail/lookBackDetail",
        state: {
          lookBackData: lookBackData,
          fromPage: "videoList" // 添加来源标记
        },
      });
    },
    [history]
  );

  const renderVideoItem = (video: VideoItem, index: number) => {
    const videoKey = `${video.camera_lens}-${video.time}`;
    const thumbnailUrl = videoThumbnails.get(videoKey);

    return (
      <div key={`${video.camera_lens}-${video.time}-${index}`} className={styles.videoItem}>
        <div className={styles.thumbnailContainer}>
          <div className={styles.thumbnail} onClick={() => lookBackDetail(video)}>
            {thumbnailUrl ? (
              // 显示真实的缩略图
              <PreloadImage
                src={splitURL(thumbnailUrl)}
                alt=""
                className={styles.thumbnailImage}
                needHeader={true}
              />
            ) : null}
            {/* 占位图或加载状态 */}
            <div className={`${styles.thumbnailPlaceholder} ${thumbnailUrl ? styles.hidden : ''}`}>
            </div>

            {/* 播放图标  */}
            {thumbnailUrl && (
              <div className={styles.playIcon}>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <circle cx="12" cy="12" r="10" fill="rgba(255,255,255,0.8)" />
                  <polygon points="10,8 16,12 10,16" fill="#333" />
                </svg>
              </div>
            )}
          </div>
        </div>
        <div className={styles.timeLabel}>{generateTimeRangeLabel(video.time, video.media_duration) || video.timeLabel }</div>
      </div>
    );
  };

  return (
    <div className={styles.container}>
      <NavigatorBar
        onBack={handleBack}
        right={
          <div onClick={handleOpenEditModal} style={{ cursor: 'pointer' }}>
            <PreloadImage
              src={isDarkMode ? filterDarkIcon : filterIcon}
            />
          </div>
        }
      />
      <div className={styles.title}>存储管理</div>

      {/* 选择器容器 */}
      <div className={styles.selectorContainer}>
        {/* 摄像机选择器 */}
        <PopoverSelector
          visible={cameraPopoverVisible}
          onVisibleChange={setCameraPopoverVisible}
          value={selectedCamera}
          options={cameraOptions}
          onChange={setSelectedCamera}
          placement="bottom-start"
        >
          <div className={styles.selector}>
            <span className={styles.selectorText}>{selectedCameraLabel}</span>
            <PreloadImage
              src={dateSelect}
              className={styles.selectorArrow}
            />
          </div>
        </PopoverSelector>

        {/* 日期选择器 */}
        <div className={styles.selector} onClick={() => setDatePickerVisible(true)}>
          <span className={styles.selectorText}>{dateDisplayText}</span>
          <PreloadImage
            src={dateSelect}
            className={styles.selectorArrow}
          />
        </div>
      </div>

      {/* 视频网格 */}
      <div className={styles.content}>
        <div className={styles.videoGrid}>
          {videoData?.map((video, index) => renderVideoItem(video, index))}
        </div>
      </div>

      {/* 日期选择器弹窗 */}
      <DatePicker
        isShow={datePickerVisible}
        onCancel={() => setDatePickerVisible(false)}
        onSelect={handleDateSelect}
      />

      {/* 编辑模态框 */}
      <VideoEditModal
        visible={editModalVisible}
        onClose={handleCloseEditModal}
        videoData={videoData || []}
        videoThumbnails={videoThumbnails}
        onSaveToLocal={handleSaveToLocal}
        onDelete={handleDeleteVideos}
        generateTimeRangeLabel={generateTimeRangeLabel}
      />
    </div>
  );
}
